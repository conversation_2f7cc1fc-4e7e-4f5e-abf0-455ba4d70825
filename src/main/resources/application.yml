spring:
  application:
    name: wrpt
  profiles:
    active: test
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 1000MB
      max-request-size: 1000MB

server:
  servlet:
    context-path: /wrpt
  port: 8090

# 性能优化配置
performance:
  # 数据库查询批次大小
  batch-size: 100
  # 任务数据缓存时间（分钟）
  task-data-cache-minutes: 5
  # 点迹数据缓存时间倍数
  point-data-cache-multiplier: 2
  # 是否启用异步预计算
  enable-async-precompute: true
  # 预计算关键时间点的比例
  key-time-point-ratios: [0.0, 0.25, 0.5, 0.75, 1.0]
  # 渐进式预计算批次大小（每批计算多少个时间点）
  progressive-batch-size: 20
  # 预测性预加载窗口大小（预加载后续多少秒的数据）
  predictive-window-size: 10
  # 渐进式预计算的延迟时间（毫秒）
  progressive-delay-ms: 200
jwt:
  security:
    permit-urls: /**/login,/swagger-ui.html/**,/webjars/**,/api/v2/**,/v2/**,/swagger-resources/**,/csrf,/doc.html/**,/user/**,/ws/**,/file/**,/ops/**,/external/space/**,/external/**,/image/**,/api/**
    token-refresh-interval: 600000