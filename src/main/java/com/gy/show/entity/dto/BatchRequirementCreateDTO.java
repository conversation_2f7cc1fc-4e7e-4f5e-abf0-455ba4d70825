package com.gy.show.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量创建目标DTO
 * 用于在一个需求中批量创建1000个目标
 */
@Data
public class BatchRequirementCreateDTO {

    /**
     * 需求名称
     */
    @NotBlank(message = "需求名称不能为空")
    private String requirementName;

    /**
     * 需求类型 1 预规划任务 2 随遇接入任务
     */
    @NotNull(message = "需求类型不能为空")
    private Integer requirementType;

    /**
     * 重要程度 1 一般 2 重要
     */
    @NotNull(message = "重要程度不能为空")
    private Integer importance;

    /**
     * 需求开始时间
     */
    @NotNull(message = "需求开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 需求结束时间
     */
    @NotNull(message = "需求结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 需求描述
     */
    private String requirementComment;

    /**
     * 目标数量
     */
    @NotNull(message = "目标数量不能为空")
    private Integer targetCount;

    /**
     * 任务配置
     */
    private TaskConfig taskConfig;

    /**
     * 航迹配置
     */
    private TrackConfig trackConfig;

    @Data
    public static class TaskConfig {
        /**
         * 任务类型列表 1 遥控 2 遥测 3 测量 4 数传
         */
        private List<Integer> taskTypes;

        /**
         * 任务开始时间偏移（相对于需求开始时间，单位：分钟）
         */
        private Integer taskStartOffset = 0;

        /**
         * 任务持续时间（单位：分钟）
         */
        private Integer taskDuration = 60;

        /**
         * 重复类型 0 仅一次 1 每日重复 2 每月重复
         */
        private Integer repeatType = 0;
    }

    @Data
    public static class TrackConfig {
        /**
         * 航迹开始时间偏移（相对于需求开始时间，单位：分钟）
         */
        private Integer trackStartOffset = 0;

        /**
         * 是否使用现有预设航迹
         */
        private Boolean useExistingTrack = true;

        /**
         * 预设航迹ID前缀（如果使用现有航迹）
         */
        private String trackPresetIdPrefix;
    }
}
