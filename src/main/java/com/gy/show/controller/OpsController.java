package com.gy.show.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.common.Result;
import com.gy.show.constants.Constants;
import com.gy.show.controller.external.InteractionLogDTO;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.external.SituationTestDTO;
import com.gy.show.entity.dto.external.TelemetryDispatcherDTO;
import com.gy.show.enums.LogTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.*;
import com.gy.show.socket.UdpSender;
import com.gy.show.util.RedisUtil;
import com.gy.show.ws.FullViewTsServer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.awt.geom.Point2D;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.gy.show.constants.CacheConstant.TELEMETRY_DISPATCHER;
import static com.gy.show.controller.FlightPathGenerator.generateFlightPaths;
import static com.gy.show.controller.FlightPathGenerator.generateFlightPathsFromBasePoints;

@Slf4j
@Api(tags = "运维后台接口")
@RequestMapping("/ops")
@RestController
public class OpsController {

    @Autowired
    private OpsService opsService;

    @Autowired
    private FileService fileService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private UdpSender udpSender;

    private String globalRequirementId;

    @Autowired
    private FullViewTsServer server;

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private ExternalDataService externalDataService;

    @Autowired
    private TerminalDataService terminalDataService;

    @Autowired
    private RequirementInfoService requirementInfoService;

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private DataPresetTrackInfoService dataPresetTrackInfoService;

    @Autowired
    private DataPresetTrackService dataPresetTrackService;

    @GetMapping("/targetPush")
    @ApiOperation("测试目标态势推送")
    public Result targetPush() throws InterruptedException {
        boolean s = true;
        String msg = "{\"data\":[{\"mbId\":1834043862493249538,\"kzq\":3,\"sxj\":78.0,\"dj\":15.0,\"kzms\":50,\"targetType\":1,\"reverse\":\"r5XlkI0AAAAAABOYAAAAAA==\",\"latitude\":35.7654321,\"sddx\":0.7,\"lineStatus\":3,\"hy\":2.0,\"taskCount\":1,\"sdfx\":80.4,\"sj\":1739772605123,\"name\":\"轨迹测试名\",\"gdmx\":1,\"longitude\":120.1234567,\"tasks\":[{\"process\":-29720,\"taskSeq\":1,\"requirementId\":1,\"equipmentId\":-1964770217869318475,\"taskId\":1}],\"seq\":1,\"zy\":5.0}],\"type\":5}";

        Map<String, Object> map = commonMapper.getOne("data_ship_target", "1837788377185783810");
        map.put("targetTypeValue", "无人艇");

        JSONObject jo = JSON.parseObject(msg);

        JSONArray data = jo.getJSONArray("data");

        data.getJSONObject(0).put("bindingTarget", map);

        String jsonString = JSON.toJSONString(jo);
        while (s) {
            server.sendAll(jsonString);

            Thread.sleep(2000);
        }
        return Result.ok();
    }

    @GetMapping
    @ApiOperation("改变实时态势推送的需求id")
    public Result changeRequirement(@RequestParam("id") String id) {
        log.info("态势推送需求id改变，改变后ID为：{}", id);
        globalRequirementId = id;
        return Result.ok();
    }

    @PostMapping("/uploadFile")
    @ApiOperation("上传需求文件")
    public Result uploadFile(@RequestPart("file") MultipartFile file) {
        opsService.uploadFile(file);
        return Result.ok();
    }

    @ApiOperation("获取遥测数据转发列表")
    @GetMapping("/getTelemetryDispatcher")
    public Result getTelemetryDispatcher() {
        List<String> result = new ArrayList<>();
        for (String station : Constants.stationList) {
            String contr = RedisUtil.StringOps.get(TELEMETRY_DISPATCHER + station);

            result.add(contr);
        }
        return Result.ok(result);
    }

    @ApiOperation("修改遥测数据转发目标")
    @PostMapping("/updateTelemetryDispatcher")
    public Result updateTelemetryDispatcher(@RequestBody TelemetryDispatcherDTO telemetryDispatcherDTO) {
        RedisUtil.StringOps.set(TELEMETRY_DISPATCHER + telemetryDispatcherDTO.getTarget(), telemetryDispatcherDTO.getTarget());

        return Result.ok();
    }

    @GetMapping("/parseTle")
    @ApiOperation("解析卫星文件")
    public Result parseTle() {
        String filepath = "E:\\ZQY\\KY\\tledata.tle";
        String outputDir = "E:\\ZQY\\KY\\temp\\wrpt\\";
        List<Map<String, Object>> tles = parseTleFile(filepath);

        for (Map<String, Object> tle : tles) {
            String fileId = saveTleToFile(tle, outputDir);
            tle.put("area_id", "1834040208591765506");
            tle.put("general_id", "2");
            tle.put("code", tle.get("name"));
            tle.put("file_id", fileId);
            tle.put("start_frequency", "1");
            tle.put("end_frequency", "1");
            tle.put("end_frequency", "1");
            tle.put("business_type", 1);
            tle.put("eirp", "1");
            tle.put("work_system", "1");
            tle.put("id", IdWorker.getIdStr());
//            tle.put("antenna_type", "1");
//            tle.put("antenna_type", "1");
//            tle.put("antenna_type", "1");

            tle.remove("line1");
            tle.remove("line2");
            commonMapper.save("data_low_satellite", tle);
        }
        return Result.ok();
    }

    @GetMapping("/startPush")
    @ApiOperation("开始推送数据")
    public Result startPush() throws InterruptedException {
        udpSender.start();
        return Result.ok();
    }

    @GetMapping("/testChange")
    @ApiOperation("测试变更推送数据")
    public Result testChange(@RequestParam("changed") Boolean changed) {
        udpSender.setChanged(changed);
        return Result.ok();
    }

    @GetMapping("/testCarBz")
    @ApiOperation("测试无人车发送遥测业务数据")
    public Result testCarBz() {
        stationDataService.testCarBz();
        return Result.ok();
    }

    @GetMapping("/testCarDp")
    @ApiOperation("测试无人车发送底盘数据")
    public Result testCarDp() {
        stationDataService.testCarDp();
        return Result.ok();
    }

    @GetMapping("/pushBzData")
    @ApiOperation("模拟推送操控的业务数据到相关站")
    public Result pushBzData() {
        externalDataService.simulatePushBzData();
        return Result.ok();
    }

    private String saveTleToFile(Map<String, Object> tle, String outputDir) {
        String name = tle.get("name").toString();
        String filename = outputDir + "/" + name + ".txt";
        try (BufferedWriter bw = new BufferedWriter(new FileWriter(filename))) {
            bw.write(name);
            bw.newLine();
            bw.write(tle.get("line1").toString());
            bw.newLine();
            bw.write(tle.get("line2").toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 保存文件元数据信息
        DataFile dataFile = new DataFile();
        dataFile.setFileName(name + ".txt");
        dataFile.setFilePath("wrpt");

        fileService.save(dataFile);

        return dataFile.getId();
    }

    private List<Map<String, Object>> parseTleFile(String filepath) {
        List<Map<String, Object>> result = new LinkedList<>();
        try (BufferedReader br = new BufferedReader(new FileReader(filepath))) {
            String line;
            while ((line = br.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue;
                }

                String name = line.trim();
                String line1 = br.readLine().trim();
                String line2 = br.readLine().trim();

                Map<String, Object> tle = new HashMap<>();
                tle.put("name", name);
                tle.put("line1", line1);
                tle.put("line2", line2);
                result.add(tle);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @GetMapping("pushLog")
    @ApiOperation("发送测试交互日志")
    public Result pushLog() {
        for (int i = 0; i < 1000; i++) {
            InteractionLogDTO interactionLogDTO = new InteractionLogDTO();
            interactionLogDTO.setTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss.SSS"));
            interactionLogDTO.setType(LogTypeEnum.INFO.getMessage());
            interactionLogDTO.setMessage("向终端发送体制切换命令");

            server.sendInteractionLog(JSON.toJSONString(interactionLogDTO));

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return Result.ok();
    }

    @GetMapping("pushTestTs")
    @ApiOperation("发送测试态势生成时间")
    public Result pushTestTs() {
        for (int i = 0; i < 1000; i++) {
            SituationTestDTO situationTestDTO = new SituationTestDTO();
            situationTestDTO.setSource("无人机测控站数据");
            situationTestDTO.setReceiveTime(DateUtil.now());
            situationTestDTO.setShowTime(DateUtil.now());
            situationTestDTO.setDuration("23");
            situationTestDTO.setType("这个类型是什么");

            server.sendSituationTest(JSON.toJSONString(situationTestDTO));

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return Result.ok();
    }

    @GetMapping("/testUavStation")
    @ApiOperation("测试无人机测控站数据")
    public Result testUavStation() {
        String msg = "{\"data\":{\"sxyksl\":38.4,\"frameTail\":125,\"stationInfo\":{\"altitude\":1200.0000,\"code\":\"lieying\",\"latitude\":33.548000,\"dataType\":2,\"updateTime\":1751443645000,\"bottomRadius\":1000.0000,\"targetCount\":1,\"eirp\":20.0000,\"gt\":10.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"pitchAngle\":0.00000,\"imgUrl\":\"http://192.168.19.242:8090/wrpt/file/image/空基平台.jpg\",\"workSystem\":\"1\",\"areaId\":\"1834039733926576129\",\"createTime\":1726123661000,\"name\":\"地基无人机综合测控节点\",\"generalId\":\"13\",\"bearingAngle\":0.00000,\"typeValue\":\"中型无人机\",\"id\":\"1834121686784696321\",\"businessType\":\"2\",\"longitude\":119.905000,\"dataTypeValue\":\"空基节点\"},\"sxykpl\":1800.0,\"sxfszt\":50,\"xxfszt\":50,\"xxykpl\":2300.0,\"sxxhqd\":0.39,\"checksum\":0,\"xxycsl\":38.4,\"xxxhqd\":9.3,\"enter\":2573,\"xxsdzt\":49,\"sxsdzt\":49},\"type\":9}";

        pushTestData(msg);

        return Result.ok();
    }

    @GetMapping("/testSpaceStation")
    @ApiOperation("测试航天测控站数据")
    public Result testSpaceStation() {
        String msg = "{\"data\":[{\"ycztbsd\":2,\"wxclsd\":2,\"stationInfo\":{\"altitude\":0.0000,\"code\":\"feiting1\",\"latitude\":34.112900,\"dataType\":2,\"updateTime\":1751419827000,\"bottomRadius\":1000000.0000,\"targetCount\":1,\"eirp\":40.0000,\"gt\":5.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"pitchAngle\":0.00000,\"imgUrl\":\"http://192.168.19.242:8090/wrpt/file/image/空基平台.jpg\",\"workSystem\":\"1\",\"areaId\":\"1838030102630240258\",\"createTime\":1726123376000,\"name\":\"空基综合测控节点\",\"generalId\":\"7\",\"bearingAngle\":0.00000,\"typeValue\":\"浮空平台\",\"id\":\"1834120494834802689\",\"businessType\":\"1\",\"longitude\":116.705000,\"dataTypeValue\":\"空基节点\"},\"empty3\":0,\"clwmsd\":1,\"empty1\":0,\"empty2\":0,\"clagcdy\":0,\"cjztbsdzs\":0,\"jsykzts\":0,\"scsd\":0,\"ycwmsd\":2,\"ykfnts\":0,\"ycc\":0,\"wxycsd\":2,\"sxgzpl\":2070000000,\"scdplpl\":0,\"clzpdplpl\":0,\"ybdpl\":0,\"targetInfo\":{\"code\":\"KQ200-1\",\"updateTime\":1733971453000,\"eirp\":45.0000,\"gt\":5.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"workSystem\":\"1\",\"areaId\":\"1833390754645704706\",\"createTime\":1726993685000,\"name\":\"无人机-KQ01\",\"generalId\":\"8\",\"id\":\"1837772868386033665\",\"businessType\":\"1\"},\"yczbsd\":2,\"ycwtbsd\":2,\"scjlz\":0,\"cjwtbsdzs\":0,\"ebno\":-30,\"clzbsd\":1,\"clcn\":0,\"agc\":0,\"dqgztd\":1,\"xxgzpl\":2260000000},{\"ycztbsd\":2,\"wxclsd\":2,\"stationInfo\":{\"$ref\":\"$.data[0].stationInfo\"},\"empty3\":0,\"clwmsd\":1,\"empty1\":0,\"empty2\":0,\"clagcdy\":0,\"cjztbsdzs\":0,\"jsykzts\":0,\"scsd\":0,\"ycwmsd\":1,\"ykfnts\":0,\"ycc\":0,\"wxycsd\":2,\"sxgzpl\":2070000000,\"scdplpl\":0,\"clzpdplpl\":0,\"ybdpl\":0,\"targetInfo\":{\"workSystem\":\"34\",\"areaId\":\"1802512421513105409\",\"code\":\"456\",\"createTime\":1718588029000,\"name\":\"F35\",\"generalId\":\"8\",\"updateTime\":1733971453000,\"id\":\"1802512866138689538\",\"businessType\":\"1\",\"eirp\":45.0000,\"antennaType\":1,\"frequency\":\"0,1\"},\"yczbsd\":1,\"ycwtbsd\":1,\"scjlz\":0,\"cjwtbsdzs\":0,\"ebno\":205,\"clzbsd\":2,\"clcn\":0,\"agc\":0,\"dqgztd\":1,\"xxgzpl\":2260000000},{\"ycztbsd\":1,\"wxclsd\":1,\"stationInfo\":{\"$ref\":\"$.data[0].stationInfo\"},\"empty3\":0,\"clwmsd\":1,\"empty1\":0,\"empty2\":0,\"clagcdy\":0,\"cjztbsdzs\":0,\"jsykzts\":0,\"scsd\":0,\"ycwmsd\":1,\"ykfnts\":0,\"ycc\":0,\"wxycsd\":1,\"sxgzpl\":2070000000,\"scdplpl\":0,\"clzpdplpl\":0,\"ybdpl\":0,\"targetInfo\":{\"code\":\"ruizhua1\",\"updateTime\":1733971513000,\"eirp\":45.0000,\"gt\":15.0000,\"antennaType\":2,\"frequency\":\"0,1\",\"workSystem\":\"2\",\"areaId\":\"1833387616605863938\",\"createTime\":1725948973000,\"name\":\"无人车-锐爪01\",\"generalId\":\"9\",\"id\":\"1833388995533635585\",\"businessType\":\"1\"},\"yczbsd\":1,\"ycwtbsd\":1,\"scjlz\":0,\"cjwtbsdzs\":0,\"ebno\":284,\"clzbsd\":1,\"clcn\":0,\"agc\":0,\"dqgztd\":1,\"xxgzpl\":2260000000},{\"ycztbsd\":1,\"wxclsd\":1,\"stationInfo\":{\"$ref\":\"$.data[0].stationInfo\"},\"empty3\":0,\"clwmsd\":1,\"empty1\":0,\"empty2\":0,\"clagcdy\":0,\"cjztbsdzs\":0,\"jsykzts\":0,\"scsd\":0,\"ycwmsd\":1,\"ykfnts\":0,\"ycc\":0,\"wxycsd\":1,\"sxgzpl\":2070000000,\"scdplpl\":0,\"clzpdplpl\":0,\"ybdpl\":0,\"targetInfo\":{\"code\":\"liaowangzhe02\",\"updateTime\":1751354375000,\"eirp\":1000.0000,\"gt\":3000.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"workSystem\":\"1\",\"areaId\":\"1833390754645704706\",\"createTime\":1725949459000,\"name\":\"ZD-U-1500-1\",\"generalId\":\"10\",\"id\":\"1833391033822773250\",\"businessType\":\"1\"},\"yczbsd\":1,\"ycwtbsd\":1,\"scjlz\":0,\"cjwtbsdzs\":0,\"ebno\":343,\"clzbsd\":1,\"clcn\":0,\"agc\":0,\"dqgztd\":1,\"xxgzpl\":2260000000}],\"type\":7}";

        pushTestData(msg);

        return Result.ok();
    }

    @GetMapping("/testSpaceStation2")
    @ApiOperation("测试航天测控站数据2")
    public Result testSpaceStation2() {
        String msg = "{\"data\":[{\"ycztbsd\":2,\"wxclsd\":2,\"stationInfo\":{\"altitude\":36000000.0000,\"code\":\"tianlian1-02\",\"latitude\":0.000000,\"dataType\":3,\"updateTime\":1751358035000,\"targetCount\":4,\"eirp\":60.0000,\"gt\":10.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"pitchAngle\":30.00000,\"imgUrl\":\"http://192.168.19.242:8090/wrpt/file/image/天基平台.jpg\",\"workSystem\":1,\"areaId\":\"1823982963548037121\",\"createTime\":1727229861000,\"name\":\"天基综合测控节点\",\"generalId\":\"4\",\"bearingAngle\":20.00000,\"typeValue\":\"高轨卫星\",\"id\":\"1838763476605005826\",\"businessType\":1,\"fileId\":\"1838763250305527809\",\"longitude\":177.000000,\"dataTypeValue\":\"天基节点\"},\"empty3\":0,\"clwmsd\":2,\"empty1\":0,\"empty2\":0,\"clagcdy\":0,\"cjztbsdzs\":0,\"jsykzts\":0,\"scsd\":0,\"ycwmsd\":2,\"ykfnts\":0,\"ycc\":0,\"wxycsd\":2,\"sxgzpl\":2070000000,\"scdplpl\":0,\"clzpdplpl\":0,\"ybdpl\":0,\"targetInfo\":{\"code\":\"KQ200-1\",\"updateTime\":1733971453000,\"eirp\":45.0000,\"gt\":5.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"workSystem\":\"1\",\"areaId\":\"1833390754645704706\",\"createTime\":1726993685000,\"name\":\"无人机-KQ01\",\"generalId\":\"8\",\"id\":\"1837772868386033665\",\"businessType\":\"1\"},\"yczbsd\":2,\"ycwtbsd\":2,\"scjlz\":0,\"cjwtbsdzs\":0,\"ebno\":-16,\"clzbsd\":2,\"clcn\":0,\"agc\":0,\"dqgztd\":1,\"xxgzpl\":2260000000},{\"ycztbsd\":2,\"wxclsd\":2,\"stationInfo\":{\"$ref\":\"$.data[0].stationInfo\"},\"empty3\":0,\"clwmsd\":2,\"empty1\":0,\"empty2\":0,\"clagcdy\":0,\"cjztbsdzs\":0,\"jsykzts\":0,\"scsd\":0,\"ycwmsd\":2,\"ykfnts\":0,\"ycc\":0,\"wxycsd\":2,\"sxgzpl\":2070000000,\"scdplpl\":0,\"clzpdplpl\":0,\"ybdpl\":0,\"targetInfo\":{\"workSystem\":\"34\",\"areaId\":\"1802512421513105409\",\"code\":\"456\",\"createTime\":1718588029000,\"name\":\"F35\",\"generalId\":\"8\",\"updateTime\":1733971453000,\"id\":\"1802512866138689538\",\"businessType\":\"1\",\"eirp\":45.0000,\"antennaType\":1,\"frequency\":\"0,1\"},\"yczbsd\":2,\"ycwtbsd\":2,\"scjlz\":0,\"cjwtbsdzs\":0,\"ebno\":-30,\"clzbsd\":2,\"clcn\":0,\"agc\":0,\"dqgztd\":1,\"xxgzpl\":2260000000},{\"ycztbsd\":2,\"wxclsd\":2,\"stationInfo\":{\"$ref\":\"$.data[0].stationInfo\"},\"empty3\":0,\"clwmsd\":2,\"empty1\":0,\"empty2\":0,\"clagcdy\":0,\"cjztbsdzs\":0,\"jsykzts\":0,\"scsd\":0,\"ycwmsd\":2,\"ykfnts\":0,\"ycc\":0,\"wxycsd\":2,\"sxgzpl\":2070000000,\"scdplpl\":0,\"clzpdplpl\":0,\"ybdpl\":0,\"targetInfo\":{\"code\":\"ruizhua1\",\"updateTime\":1733971513000,\"eirp\":45.0000,\"gt\":15.0000,\"antennaType\":2,\"frequency\":\"0,1\",\"workSystem\":\"2\",\"areaId\":\"1833387616605863938\",\"createTime\":1725948973000,\"name\":\"无人车-锐爪01\",\"generalId\":\"9\",\"id\":\"1833388995533635585\",\"businessType\":\"1\"},\"yczbsd\":2,\"ycwtbsd\":2,\"scjlz\":0,\"cjwtbsdzs\":0,\"ebno\":-25,\"clzbsd\":2,\"clcn\":0,\"agc\":0,\"dqgztd\":1,\"xxgzpl\":2260000000},{\"ycztbsd\":2,\"wxclsd\":2,\"stationInfo\":{\"$ref\":\"$.data[0].stationInfo\"},\"empty3\":0,\"clwmsd\":2,\"empty1\":0,\"empty2\":0,\"clagcdy\":0,\"cjztbsdzs\":0,\"jsykzts\":0,\"scsd\":0,\"ycwmsd\":2,\"ykfnts\":0,\"ycc\":0,\"wxycsd\":2,\"sxgzpl\":2070000000,\"scdplpl\":0,\"clzpdplpl\":0,\"ybdpl\":0,\"targetInfo\":{\"code\":\"liaowangzhe02\",\"updateTime\":1751354375000,\"eirp\":1000.0000,\"gt\":3000.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"workSystem\":\"1\",\"areaId\":\"1833390754645704706\",\"createTime\":1725949459000,\"name\":\"ZD-U-1500-1\",\"generalId\":\"10\",\"id\":\"1833391033822773250\",\"businessType\":\"1\"},\"yczbsd\":2,\"ycwtbsd\":2,\"scjlz\":0,\"cjwtbsdzs\":0,\"ebno\":-30,\"clzbsd\":2,\"clcn\":0,\"agc\":0,\"dqgztd\":1,\"xxgzpl\":2260000000}],\"type\":7}";

        pushTestData(msg);

        return Result.ok();
    }

    @GetMapping("/testTerminalStation")
    @ApiOperation("测试终端数据1")
    public Result testTerminalStation() {
        String msg = "{\"data\":{\"1\":{\"nodeRelation\":{\"node4\":99,\"node5\":1,\"node2\":99,\"node3\":99,\"node0\":99,\"node1\":0},\"terCurrentMode\":\"2\",\"terCurrentNode\":\"1834055782248001538\",\"bindingTarget\":{\"code\":\"WRXL-1\",imgUrl\n" +
                ": \n" +
                "\"http://192.168.19.242:8090/wrpt/file/image/无人艇.jpg\",\"dataType\":6,\"updateTime\":1751354098000,\"eirp\":50.0000,\"gt\":10.0000,\"antennaType\":2,\"frequency\":\"0,1\",\"targetTypeValue\":\"无人车\",\"workSystem\":\"2\",\"areaId\":\"1833390754645704706\",\"createTime\":1726996855000,\"name\":\"WZ5603-2\",\"generalId\":\"9\",\"id\":\"1837786164791087105\",\"businessType\":\"1\"},\"spaceFrame\":{\"KP2CodeLockCl\":0,\"KP2WaveLockYk\":0,\"KP2BitLockYk\":0,\"KP2FrameLockYk\":0,\"KP2WaveLockCl\":0,\"KP2CodeLockYk\":0,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"KP2BitLockCl\":0,\"KP2FrameLockCl\":0},\"machineFrame\":{\"syFPGATemp\":87.85,\"randomVersion\":\"20241218\",\"ckFPGAVolt\":0.96,\"fddLaunchPllLock\":1,\"tddTemperature\":74,\"tddLaunchPllLock\":1,\"fddRevPllLock\":1,\"tddRevPllLock\":1,\"syFPGAVolt\":0.96,\"rxFreq\":30000.0,\"sybwzs\":152,\"tddElectricCurrent\":0.5,\"fddAttenuation\":0,\"rsFreq\":1350.0,\"ckFPGATemp\":108.89,\"fddTemperature\":77,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAA=\",\"controlVersion\":\"20240002\",\"tddLockedStatus\":3,\"sybxzb\":10.78,\"txFreq\":21000.0,\"tddAttenuation\":0,\"sybphpp\":-313,\"fddElectricCurrent\":1.6,\"cmd\":71,\"fddLockedStatus\":3},\"spectrumFrame\":{\"endFrequency\":30050.0,\"spectrumValues\":\"2NLW2dTY1NfR19bX1tbU2dXV1tfT1dXZ1NPW1dfV19XV1dbV19LU1dXW1dLS1dTW1tTY09nW09XT0tTS1NLU09TW2dnW1tbS1dbY1dXV1tTV19jY1dbX19jS1dLU09XU2NXX1dfY1dHV09bT1tTT1NbQ2dbV2NTW1tfV1tnW09fU19fR09bV0tfY1dXW1dfZ1tXV2dTW1dXW1tbY1NXX1tjW1tbW1tXY19TY1dbX1dbX19nW1NbV19LV2tbW1tPW1tXW2dnX2dfV19LX19XV19TU1djW2tTX19jZ1tXX1drW1tnW2NbZ1dXY2NbX1tfV2djV2djW19fX1tTV2NfV19bW1dfX2tvU2djX2NXY19bY2dnY2NfW2tnZ2dfX2drZ19rU19bW19bV2tfY19fZ19jX2NrY2tvV19na19XW19nV2dXW29XY2NfY2NjY1tXY1tba2NfX2drX29na18/V1NXb1dbZ1dnX1dbY1NjX29nW2NfZ19nW1dbZ1tfX19ba1tXW29bU2NTX09bV19bW1NfT29XV09XV2NjV2dfT1NLT19PU1dfV2NfW1dXU0tjY1NjU19jV1dXU1dTW1tfV2djU1tTX0NXU1NXS09bX2dPY19DR1NXV1dXU09bZ1tbX19XX2NbZ1NfT1dnY2dfW2dLU19PX19TV09bW1tfX29Q=\",\"interval\":0.195,\"typeValue\":\"Ka频段\",\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"type\":3,\"seq\":1,\"startFrequency\":29950.0},\"antiJammingFrame\":{\"grdk\":0,\"cglldh\":0,\"grlx\":0,\"grxh\":0,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"grqd\":-127,\"grpd\":0,\"xhdk\":0,\"kgrskqzt\":0,\"kgrzxqk\":1,\"yyxhyw\":0,\"kgrsdjcjg\":0,\"yyxhqd\":-15,\"xhpd\":0,\"seq\":1},\"createLinkDataFrame\":{\"altitude\":0.0,\"apply\":0,\"latitude\":33.55308,\"applyState\":1,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"longitude\":119.385285}}},\"type\":6}";

        pushTestData(msg);

        return Result.ok();
    }

    @GetMapping("/testTerminalStation2")
    @ApiOperation("测试终端数据2")
    public Result testTerminalStation2() {
        String msg = "{\"data\":{\"5\":{\"nodeRelation\":{\"node4\":1,\"node5\":0,\"node2\":99,\"node3\":99,\"node0\":1,\"node1\":1},\"terCurrentMode\":\"1\",\"terCurrentNode\":\"1834120494834802689\",\"bindingTarget\":{\"code\":\"MUTT-1\",\"dataType\":6,\"updateTime\":1751354096000,\"eirp\":40.0000,\"gt\":12.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"targetTypeValue\":\"无人车\",\"workSystem\":\"1\",\"areaId\":\"1833387616605863938\",\"createTime\":1726995192000,\"name\":\"WZ5603-1\",\"generalId\":\"9\",\"id\":\"1837779187331502082\",\"businessType\":\"1\"},\"spaceFrame\":{\"KP2CodeLockCl\":1,\"KP2WaveLockYk\":1,\"KP2BitLockYk\":1,\"KP2FrameLockYk\":0,\"KP2WaveLockCl\":1,\"KP2CodeLockYk\":1,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"KP2BitLockCl\":1,\"KP2FrameLockCl\":1},\"machineFrame\":{\"syFPGATemp\":69.02,\"randomVersion\":\"20241218\",\"ckFPGAVolt\":0.97,\"fddLaunchPllLock\":1,\"tddTemperature\":62,\"tddLaunchPllLock\":1,\"fddRevPllLock\":1,\"tddRevPllLock\":1,\"syFPGAVolt\":0.97,\"rxFreq\":2070.0,\"sybwzs\":45,\"tddElectricCurrent\":0.5,\"fddAttenuation\":0,\"rsFreq\":1350.0,\"ckFPGATemp\":87.35,\"fddTemperature\":62,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAA=\",\"controlVersion\":\"20240002\",\"tddLockedStatus\":3,\"sybxzb\":12.16,\"txFreq\":2260.0,\"tddAttenuation\":0,\"sybphpp\":312,\"fddElectricCurrent\":1.2,\"cmd\":71,\"fddLockedStatus\":3},\"spectrumFrame\":{\"endFrequency\":0.0,\"spectrumValues\":\"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\",\"interval\":0.0,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"type\":0,\"seq\":0,\"startFrequency\":0.0},\"antiJammingFrame\":{\"grdk\":0,\"cglldh\":0,\"grlx\":0,\"grxh\":0,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"grqd\":0,\"grpd\":0,\"xhdk\":0,\"kgrskqzt\":0,\"kgrzxqk\":0,\"yyxhyw\":0,\"kgrsdjcjg\":0,\"yyxhqd\":0,\"xhpd\":0,\"seq\":0},\"curStationName\":\"空基综合测控节点\",\"createLinkDataFrame\":{\"altitude\":0.0,\"apply\":0,\"latitude\":33.55308,\"applyState\":1,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"longitude\":119.385285}}},\"type\":6}";

        // 将字符串解析为 JSON 对象
        JSONObject jsonObject = JSONObject.parseObject(msg);

        // 获取 createlink 对象
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject node5 = data.getJSONObject("5");
        JSONObject createLinkDataFrame = node5.getJSONObject("createLinkDataFrame");

        // 生成随机经纬度（中国区域范围）
        double latitude = 18 + new Random().nextDouble() * (54 - 18);  // 纬度
        double longitude = 73 + new Random().nextDouble() * (135 - 73); // 经度

        // 更新经纬度
        createLinkDataFrame.put("latitude", latitude);
        createLinkDataFrame.put("longitude", longitude);

        // 转回 JSON 字符串
        String updatedMsg = jsonObject.toJSONString();

        // 推送处理后的数据
        pushTestData(updatedMsg);

        return Result.ok();
    }

//    @GetMapping("/testTerminalStation2")
//    @ApiOperation("测试终端数据2")
//    public Result testTerminalStation2() {
//        String msg = "{\"data\":{\"5\":{\"nodeRelation\":{\"node4\":1,\"node5\":0,\"node2\":99,\"node3\":99,\"node0\":1,\"node1\":1},\"terCurrentMode\":\"1\",\"terCurrentNode\":\"1834120494834802689\",\"bindingTarget\":{\"code\":\"MUTT-1\",\"dataType\":6,\"updateTime\":1751354096000,\"eirp\":40.0000,\"gt\":12.0000,\"antennaType\":1,\"frequency\":\"0,1\",\"targetTypeValue\":\"无人车\",\"workSystem\":\"1\",\"areaId\":\"1833387616605863938\",\"createTime\":1726995192000,\"name\":\"WZ5603-1\",\"generalId\":\"9\",\"id\":\"1837779187331502082\",\"businessType\":\"1\"},\"spaceFrame\":{\"KP2CodeLockCl\":1,\"KP2WaveLockYk\":1,\"KP2BitLockYk\":1,\"KP2FrameLockYk\":0,\"KP2WaveLockCl\":1,\"KP2CodeLockYk\":1,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"KP2BitLockCl\":1,\"KP2FrameLockCl\":1},\"machineFrame\":{\"syFPGATemp\":69.02,\"randomVersion\":\"20241218\",\"ckFPGAVolt\":0.97,\"fddLaunchPllLock\":1,\"tddTemperature\":62,\"tddLaunchPllLock\":1,\"fddRevPllLock\":1,\"tddRevPllLock\":1,\"syFPGAVolt\":0.97,\"rxFreq\":2070.0,\"sybwzs\":45,\"tddElectricCurrent\":0.5,\"fddAttenuation\":0,\"rsFreq\":1350.0,\"ckFPGATemp\":87.35,\"fddTemperature\":62,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAA=\",\"controlVersion\":\"20240002\",\"tddLockedStatus\":3,\"sybxzb\":12.16,\"txFreq\":2260.0,\"tddAttenuation\":0,\"sybphpp\":312,\"fddElectricCurrent\":1.2,\"cmd\":71,\"fddLockedStatus\":3},\"spectrumFrame\":{\"endFrequency\":0.0,\"spectrumValues\":\"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\",\"interval\":0.0,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"type\":0,\"seq\":0,\"startFrequency\":0.0},\"antiJammingFrame\":{\"grdk\":0,\"cglldh\":0,\"grlx\":0,\"grxh\":0,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"grqd\":0,\"grpd\":0,\"xhdk\":0,\"kgrskqzt\":0,\"kgrzxqk\":0,\"yyxhyw\":0,\"kgrsdjcjg\":0,\"yyxhqd\":0,\"xhpd\":0,\"seq\":0},\"curStationName\":\"空基综合测控节点\",\"createLinkDataFrame\":{\"altitude\":0.0,\"apply\":0,\"latitude\":33.55308,\"applyState\":1,\"reverse\":\"AQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAA==\",\"longitude\":119.385285}}},\"type\":6}";
//
//        pushTestData(msg);
//
//        return Result.ok();
//    }

    private void pushTestData(String msg) {
        JSONObject jsonObject = JSON.parseObject(msg);


        new Thread(() -> {
            for (int i = 0; i < 1; i++) {
                server.sendAll(JSON.toJSONString(jsonObject));

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    @GetMapping("/testSwitchStation")
    @ApiOperation("测试站点切换")
    public Result testSwitchStation() {
        terminalDataService.testSwitchStation();

        return Result.ok();
    }

    @PostMapping("/generalTargetUav")
    @ApiOperation("生成测试目标无人机")
    public Result generalTarget() {
        for (int i = 1; i < 361; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("general_id", 8);
            map.put("id", IdWorker.getIdStr());
            map.put("area_id", "1833389588553695233");
            map.put("name", "无人机-SH0" + i);
            map.put("code", "SH0" + i);
            map.put("frequency", "1,2,6,3,4,5,0");
            map.put("business_type", "1,2,3,4");
            map.put("eirp", "50");
            map.put("gt", "50");
            map.put("antenna_type", "2");
            map.put("work_system", "1,2,3");
            commonMapper.save("data_uav_target", map);
        }

        return Result.ok();
    }

    @PostMapping("/generalTargetD")
    @ApiOperation("生成测试目标巡飞弹")
    public Result generalTargetD() {
        for (int i = 1; i < 200; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("general_id", 11);
            map.put("id", IdWorker.getIdStr());
            map.put("area_id", "1833389588553695233");
            map.put("name", "飞龙60A-" + i);
            map.put("code", "60A" + i);
            map.put("frequency", "1,2,6,3,4");
            map.put("business_type", "1,2,3,4");
            map.put("eirp", "50");
            map.put("gt", "50");
            map.put("antenna_type", "2");
            map.put("work_system", "1,2,3");
            commonMapper.save("data_missile_target", map);
        }

        return Result.ok();
    }

    @PostMapping("/generalTargetCar")
    @ApiOperation("生成测试目标无人车")
    public Result generalTargetCar() {
        for (int i = 1; i < 200; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("general_id", 9);
            map.put("id", IdWorker.getIdStr());
            map.put("area_id", "1833389588553695233");
            map.put("name", "无人车-SK" + i);
            map.put("code", "SK" + i);
            map.put("frequency", "1,2,6,3,4");
            map.put("business_type", "1,2,3,4");
            map.put("eirp", "50");
            map.put("gt", "50");
            map.put("antenna_type", "2");
            map.put("work_system", "1,2,3");
            commonMapper.save("data_car_target", map);
        }

        return Result.ok();
    }

    @PostMapping("/generalStation")
    @ApiOperation("生成测试地基测控站")
    public Result generalStation() {
        for (int i = 1; i < 30; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("general_id", 5);
            map.put("id", IdWorker.getIdStr());
            map.put("area_id", "1879104437813809153");
            map.put("name", "地基测控站-DJ" + i);
            map.put("code", "DJ" + i);
            map.put("frequency", "0,1");
            map.put("target_count", "4");
            map.put("business_type", "1");
            map.put("eirp", "50");
            map.put("gt", "50");
            map.put("antenna_type", "2");
            map.put("work_system", "1,2,3");
            Point2D.Double point = generateRandomPointInTaiwanStrait();
            map.put("longitude", point.getX());
            map.put("latitude", point.getY());
            map.put("altitude", 0);
            map.put("radius", 5000);
            commonMapper.save("data_fixed_station", map);
        }

        return Result.ok();
    }

    @PostMapping("/generalTargetShip")
    @ApiOperation("生成测试目标无人艇")
    public Result generalTargetShip() {
        for (int i = 1; i < 300; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("general_id", 10);
            map.put("id", IdWorker.getIdStr());
            map.put("area_id", "1833389588553695233");
            map.put("name", "无人艇-SH" + i);
            map.put("code", "SH" + i);
            map.put("frequency", "1,2,6,3,4");
            map.put("business_type", "1,2,3,4");
            map.put("eirp", "50");
            map.put("gt", "50");
            map.put("antenna_type", "2");
            map.put("work_system", "1,2,3");
            commonMapper.save("data_ship_target", map);
        }

        return Result.ok();
    }

    @PostMapping("/generalTrack")
    @ApiOperation("生成测试航迹")
    public Result generalTrack(@RequestParam("presetId") String presetId,
                              @RequestParam(value = "numPaths", defaultValue = "1000") int numPaths,
                              @RequestParam(value = "speed", defaultValue = "400") double speed) {
        // 从数据库查询指定presetId的航迹点作为基础点
        List<DataPresetTrack> baseTrackPoints = dataPresetTrackService.list(
            Wrappers.<DataPresetTrack>lambdaQuery()
                .eq(DataPresetTrack::getPresetId, presetId)
                .orderByAsc(DataPresetTrack::getTime)
        );

        DataPresetTrackInfo info = dataPresetTrackInfoService.getById(presetId);

        if (baseTrackPoints.isEmpty()) {
            return Result.error("指定的presetId对应的航迹数据不存在");
        }

        // 将数据库中的航迹点转换为基础点数组
        double[][] basePoints = baseTrackPoints.stream()
            .map(track -> new double[]{
                track.getLongitude().doubleValue(),
                track.getLatitude().doubleValue()
            })
            .toArray(double[][]::new);

        // 生成航迹（使用数据库中的基础点）
        List<List<FlightPathGenerator.TrackPoint>> flightPaths = generateFlightPathsFromBasePoints(basePoints, numPaths, speed);

        for (int i = 0; i < numPaths; i++) {
            DataPresetTrackInfo trackInfo = new DataPresetTrackInfo();
            trackInfo.setName(info.getName() + i);
            trackInfo.setType(1);
            trackInfo.setSpeed(BigDecimal.valueOf(speed));
            trackInfo.setViewHeight(1000000.0);

            dataPresetTrackInfoService.save(trackInfo);

            System.out.println("航迹 " + (i + 1) + ":");
            List<FlightPathGenerator.TrackPoint> trackPoints = flightPaths.get(i);
            for (FlightPathGenerator.TrackPoint point : trackPoints) {
                DataPresetTrack track = new DataPresetTrack();
                track.setLongitude(BigDecimal.valueOf(point.longitude));
                track.setLatitude(BigDecimal.valueOf(point.latitude));
                track.setAltitude(BigDecimal.valueOf(2000));
                track.setTime(point.time);

                track.setPresetId(trackInfo.getId());

                dataPresetTrackService.save(track);
            }
        }

        return Result.ok();
    }

    @GetMapping("/deleteOldTask")
    @ApiOperation("删除任务脏数据")
    public Result deleteOldTask() {
        List<RequirementInfo> list = requirementInfoService.list();

        List<String> xqIds = list.stream()
                .map(RequirementInfo::getId)
                .collect(Collectors.toList());

        requirementTaskService.remove(Wrappers.<RequirementTask>lambdaQuery().notIn(RequirementTask::getRequirementId, xqIds));

        return Result.ok();
    }

    @GetMapping("/deleteOldTrack")
    @ApiOperation("删除任务脏数据")
    public Result deleteOldTrack() {
        List<DataPresetTrackInfo> infos = dataPresetTrackInfoService.list(Wrappers.<DataPresetTrackInfo>lambdaQuery().like(DataPresetTrackInfo::getName, "台海侦查"));
        List<String> ids = infos.stream()
                .map(DataPresetTrackInfo::getId)
                .collect(Collectors.toList());

        for (String id : ids) {
            dataPresetTrackService.deleteTrackById(id);
        }
        dataPresetTrackInfoService.removeByIds(ids);
        return Result.ok();
    }

    public String getGlobalRequirementId() {
        return globalRequirementId;
    }

    public void setGlobalRequirementId(String globalRequirementId) {
        this.globalRequirementId = globalRequirementId;
    }

    private static final double MIN_LAT = 23.0;
    private static final double MAX_LAT = 24.5;
    private static final double MIN_LNG = 118.5;
    private static final double MAX_LNG = 120.0;

    private static final Random random = new Random();

    /**
     * 生成均匀分布在台海区域的经纬度点
     * @return Point2D.Double，x 是经度，y 是纬度
     */
    public static Point2D.Double generateRandomPointInTaiwanStrait() {
        double lat = MIN_LAT + (MAX_LAT - MIN_LAT) * random.nextDouble();
        double lng = MIN_LNG + (MAX_LNG - MIN_LNG) * random.nextDouble();
        return new Point2D.Double(lng, lat); // 经度, 纬度
    }

    public static void main(String[] args) {
        generalPoints();
    }

    private static final double OFFSET_RANGE = 0.01;  // 最大偏移量（约1公里）
    private static final int NUM_PATHS = 300;
    private static final double[][] BASE_POINTS = {
            {118.589833, 24.57179},
            {118.730989, 24.389579},
            {119.249827, 24.175475},
            {119.815942, 23.92331}
    };

    public static List<List<double[]>> generalPoints() {
        List<List<double[]>> allPaths = new LinkedList<>();
        Random random = new Random();

        for (int i = 0; i < NUM_PATHS; i++) {
            List<double[]> path = new LinkedList<>();

            for (double[] basePoint : BASE_POINTS) {
                // 生成随机偏移量（-OFFSET_RANGE 到 +OFFSET_RANGE）
                double offsetLon = (random.nextDouble() * 2 - 1) * OFFSET_RANGE;
                double offsetLat = (random.nextDouble() * 2 - 1) * OFFSET_RANGE;

                // 创建新点并应用偏移
                double[] newPoint = {
                        basePoint[0] + offsetLon,
                        basePoint[1] + offsetLat
                };
                path.add(newPoint);
            }
            allPaths.add(path);
        }

        return allPaths;
    }

    private static void printPaths(List<List<double[]>> paths) {
        for (int i = 0; i < paths.size(); i++) {
            System.out.println("航迹 " + (i + 1) + ":");
            List<double[]> path = paths.get(i);
            for (double[] point : path) {
                System.out.printf("  %.6f,%.6f%n", point[0], point[1]);
            }
            System.out.println();
        }
    }

}
class FlightPathGenerator {
    private static final double OFFSET_RANGE = 0.01;  // 最大偏移量（约1公里）
    private static final double EARTH_RADIUS = 6378137;  // 地球半径（米）
    private static final double[][] BASE_POINTS = {
            {118.589833, 24.57179},
            {118.730989, 24.389579},
            {119.249827, 24.175475},
            {119.815942, 23.92331}
    };

    // 航迹点类（经度、纬度、时间）
    static class TrackPoint {
        double longitude;
        double latitude;
        int time;  // 时间（秒）

        public TrackPoint(double longitude, double latitude, int time) {
            this.longitude = longitude;
            this.latitude = latitude;
            this.time = time;
        }

        @Override
        public String toString() {
            return String.format("%.6f,%.6f,%d", longitude, latitude, time);
        }
    }

    public static void main(String[] args) {
        // 生成航迹（参数：航迹数量，速度km/h）
        List<List<TrackPoint>> flightPaths = generateFlightPaths(300, 800);

        // 打印结果
        for (int i = 0; i < flightPaths.size(); i++) {
            System.out.println("航迹 " + (i + 1) + ":");
            for (TrackPoint point : flightPaths.get(i)) {
                System.out.println("  " + point);
            }
            System.out.println();
        }
    }

    /**
     * 生成带时间信息的航迹
     * @param numPaths 航迹数量
     * @param speed 速度（公里/小时）
     * @return 航迹列表
     */
    public static List<List<TrackPoint>> generateFlightPaths(int numPaths, double speed) {
        List<List<TrackPoint>> allPaths = new ArrayList<>();
        Random random = new Random();

        for (int i = 0; i < numPaths; i++) {
            // 生成基础航迹点（无时间信息）
            List<double[]> basePath = new ArrayList<>();
            for (double[] basePoint : BASE_POINTS) {
                double offsetLon = (random.nextDouble() * 2 - 1) * OFFSET_RANGE;
                double offsetLat = (random.nextDouble() * 2 - 1) * OFFSET_RANGE;
                basePath.add(new double[]{
                        basePoint[0] + offsetLon,
                        basePoint[1] + offsetLat
                });
            }

            // 添加时间信息
            allPaths.add(addTimeInformation(basePath, speed));
        }
        return allPaths;
    }

    /**
     * 根据指定的基础点生成带时间信息的航迹
     * @param basePoints 基础航迹点数组
     * @param numPaths 航迹数量
     * @param speed 速度（公里/小时）
     * @return 航迹列表
     */
    public static List<List<TrackPoint>> generateFlightPathsFromBasePoints(double[][] basePoints, int numPaths, double speed) {
        List<List<TrackPoint>> allPaths = new ArrayList<>();
        Random random = new Random();

        for (int i = 0; i < numPaths; i++) {
            // 生成基础航迹点（无时间信息）
            List<double[]> basePath = new ArrayList<>();
            for (double[] basePoint : basePoints) {
                double offsetLon = (random.nextDouble() * 2 - 1) * OFFSET_RANGE;
                double offsetLat = (random.nextDouble() * 2 - 1) * OFFSET_RANGE;
                basePath.add(new double[]{
                        basePoint[0] + offsetLon,
                        basePoint[1] + offsetLat
                });
            }

            // 添加时间信息
            allPaths.add(addTimeInformation(basePath, speed));
        }
        return allPaths;
    }

    /**
     * 为航迹添加时间信息
     * @param basePath 基础航迹点（只有经纬度）
     * @param speed 速度（公里/小时）
     * @return 带时间信息的航迹
     */
    private static List<TrackPoint> addTimeInformation(List<double[]> basePath, double speed) {
        List<TrackPoint> timedPath = new ArrayList<>();
        int cumulativeTime = 0;  // 累计时间（秒）

        for (int i = 0; i < basePath.size(); i++) {
            double[] currentPoint = basePath.get(i);

            // 计算与前一点的距离（第一个点距离为0）
            double distance = 0;
            if (i > 0) {
                double[] prevPoint = basePath.get(i - 1);
                distance = calculateDistance(
                        prevPoint[0], prevPoint[1],
                        currentPoint[0], currentPoint[1]
                );
            }

            // 计算时间增量（秒）
            int timeIncrement = (i == 0) ? 0 : (int) ((distance / speed) * 3600);
            cumulativeTime += timeIncrement;

            timedPath.add(new TrackPoint(
                    currentPoint[0],
                    currentPoint[1],
                    cumulativeTime
            ));
        }
        return timedPath;
    }

    /**
     * 计算两点间距离（Haversine公式）
     * @param lng1 经度1
     * @param lat1 纬度1
     * @param lng2 经度2
     * @param lat2 纬度2
     * @return 距离（公里）
     */
    private static double calculateDistance(double lng1, double lat1, double lng2, double lat2) {
        // 将角度转换为弧度
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double radLng1 = Math.toRadians(lng1);
        double radLng2 = Math.toRadians(lng2);

        // 经纬度差值
        double latDiff = radLat1 - radLat2;
        double lngDiff = radLng1 - radLng2;

        // Haversine公式
        double a = Math.pow(Math.sin(latDiff / 2), 2) +
                Math.cos(radLat1) * Math.cos(radLat2) *
                        Math.pow(Math.sin(lngDiff / 2), 2);

        double distance = 2 * EARTH_RADIUS * Math.asin(Math.sqrt(a));
        return distance / 1000;  // 转换为公里
    }
}
