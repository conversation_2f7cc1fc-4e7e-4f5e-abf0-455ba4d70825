package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dos.RequirementInfo;
import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.service.BatchRequirementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 批量目标控制器
 */
@Slf4j
@Api(tags = "批量目标管理")
@RestController
@RequestMapping("/api/batch-requirement")
public class BatchRequirementController {

    @Autowired
    private BatchRequirementService batchRequirementService;

    @ApiOperation("批量创建目标（在一个需求中创建多个目标）")
    @PostMapping("/create-targets")
    public Result batchCreateTargets(@Valid @RequestBody BatchRequirementCreateDTO batchConfig) {
        try {
            log.info("接收到批量创建目标请求，在一个需求中创建{}个目标", batchConfig.getTargetCount());

            RequirementInfo requirement = batchRequirementService.batchCreateTargets(batchConfig);

            log.info("批量创建目标完成，在需求{}中成功创建{}个目标",
                requirement.getRequirementName(), batchConfig.getTargetCount());
            return Result.ok(requirement, "批量创建目标成功");

        } catch (Exception e) {
            log.error("批量创建目标失败", e);
            return Result.error("批量创建目标失败：" + e.getMessage());
        }
    }
}
