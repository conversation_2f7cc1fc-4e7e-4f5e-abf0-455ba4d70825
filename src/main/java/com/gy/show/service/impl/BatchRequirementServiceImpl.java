package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.DataPresetTrackInfo;
import com.gy.show.entity.dos.RequirementInfo;
import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.BatchRequirementService;
import com.gy.show.service.DataGeneralService;
import com.gy.show.service.DataPresetTrackInfoService;
import com.gy.show.service.RequirementInfoService;
import com.gy.show.util.BatchRequirementCreator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批量目标服务实现类
 */
@Slf4j
@Service
public class BatchRequirementServiceImpl implements BatchRequirementService {

    @Autowired
    private RequirementInfoService requirementInfoService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private DataPresetTrackInfoService dataPresetTrackInfoService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private BatchRequirementCreator batchRequirementCreator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RequirementInfo batchCreateTargets(BatchRequirementCreateDTO batchConfig) {
        log.info("开始批量创建目标，在一个需求中创建{}个目标", batchConfig.getTargetCount());

        try {
            // 1. 验证配置
            validateBatchConfig(batchConfig);

            // 2. 按数据类型分组获取目标和航迹数据
            Map<String, BatchRequirementCreator.TargetTypeData> targetTypeDataMap = new HashMap<>();

            for (BatchRequirementCreateDTO.TargetTypeConfig typeConfig : batchConfig.getTargetTypeConfigs()) {
                // 获取指定数据类型的目标数据
                Map<String, Object> targetData = getTargetIdsByDataType(typeConfig.getDataType(), typeConfig.getCount(), typeConfig.getTargetNamePrefix());
                List<String> targetIds = (List<String>) targetData.get("targetIds");
                List<String> generalIds = (List<String>) targetData.get("generalIds");

                // 获取匹配的航迹数据
                List<String> trackIds = getTrackIdsByNamePrefix(typeConfig.getTrackNamePrefix(), typeConfig.getCount());

                // 封装目标类型数据
                BatchRequirementCreator.TargetTypeData typeData = new BatchRequirementCreator.TargetTypeData();
                typeData.setTargetIds(targetIds);
                typeData.setTrackIds(trackIds);
                typeData.setGeneralIds(generalIds);

                targetTypeDataMap.put(typeConfig.getDataType() + typeConfig.getTrackNamePrefix(), typeData);

                log.info("数据类型{}：获取{}个目标，{}个航迹", typeConfig.getDataType(), targetIds.size(), trackIds.size());
            }

            // 3. 生成包含多个目标的需求
            RequirementInfoDTO requirement = batchRequirementCreator.createRequirementWithBatchTargets(
                batchConfig, targetTypeDataMap);

            // 4. 创建需求（包含所有目标）
            RequirementInfo createdRequirement = requirementInfoService.addRequirementInfo(requirement);

            log.info("批量创建目标完成，在需求{}中成功创建{}个目标",
                createdRequirement.getRequirementName(), batchConfig.getTargetCount());
            return createdRequirement;

        } catch (Exception e) {
            log.error("批量创建目标失败", e);
            throw new ServiceException("批量创建目标失败：" + e.getMessage());
        }
    }
    
    /**
     * 验证批量创建配置
     */
    private void validateBatchConfig(BatchRequirementCreateDTO batchConfig) {
        if (batchConfig.getTargetTypeConfigs() == null || batchConfig.getTargetTypeConfigs().isEmpty()) {
            throw new ServiceException("目标类型配置不能为空");
        }

        // 验证总数量是否匹配
        int totalConfigCount = batchConfig.getTargetTypeConfigs().stream()
            .mapToInt(BatchRequirementCreateDTO.TargetTypeConfig::getCount)
            .sum();

        if (totalConfigCount != batchConfig.getTargetCount()) {
            throw new ServiceException("目标类型配置的总数量(" + totalConfigCount + ")与目标总数量(" + batchConfig.getTargetCount() + ")不匹配");
        }

        // 验证数据类型是否有效
        for (BatchRequirementCreateDTO.TargetTypeConfig config : batchConfig.getTargetTypeConfigs()) {
            if (!Arrays.asList(4, 5, 6, 7).contains(config.getDataType())) {
                throw new ServiceException("不支持的数据类型：" + config.getDataType() + "，仅支持4(无人机)、5(无人艇)、6(无人车)、7(弹)");
            }
        }
    }

    /**
     * 根据数据类型获取目标ID列表
     */
    private Map<String, Object> getTargetIdsByDataType(Integer dataType, Integer count, String targetNamePrefix) {
        Map<String, Object> res = new HashMap<>();
        List<String> targetIds = new ArrayList<>();
        List<String> generalIds = new ArrayList<>();

        // 查询指定数据类型的数据表
        List<DataGeneral> generals = dataGeneralService.list(
            Wrappers.<DataGeneral>lambdaQuery()
                .eq(DataGeneral::getDataType, dataType)
        );

        if (generals.isEmpty()) {
            throw new ServiceException("未找到数据类型" + dataType + "对应的数据表");
        }

        int currentCount = 0;
        for (DataGeneral general : generals) {
            if (currentCount >= count) {
                break;
            }

            // 从表中查询目标数据
            int pageSize = Math.min(count - currentCount, 500); // 每次最多查询500条
            IPage<Map<String, Object>> page = new Page<>(1, pageSize);
            IPage<Map<String, Object>> result = commonMapper.getData(page, general.getTableName(), null, null, null);
            List<Map<String, Object>> filterRes = result.getRecords().stream()
                    .filter(record -> record.get("name").toString().startsWith(targetNamePrefix))
                    .collect(Collectors.toList());

            result.setRecords(filterRes);

            List<Map<String, Object>> targets = result.getRecords();

            for (Map<String, Object> target : targets) {
                if (currentCount >= count) {
                    break;
                }
                targetIds.add(target.get("id").toString());
                generalIds.add(general.getId()); // 使用DataGeneral的ID作为generalId
                currentCount++;
            }
        }

        if (targetIds.size() < count) {
            throw new ServiceException("数据类型" + dataType + "的目标数量不足，需要" + count + "个，实际只有" + targetIds.size() + "个");
        }

        res.put("targetIds", targetIds.subList(0, count));
        res.put("generalIds", generalIds.subList(0, count));
        return res;
    }
    
    /**
     * 根据航迹名称前缀获取航迹ID列表
     */
    private List<String> getTrackIdsByNamePrefix(String namePrefix, Integer count) {
        List<DataPresetTrackInfo> tracks = dataPresetTrackInfoService.list(
            Wrappers.<DataPresetTrackInfo>lambdaQuery()
                .like(DataPresetTrackInfo::getName, namePrefix)
                .orderByDesc(DataPresetTrackInfo::getCreateTime)
                .last("LIMIT " + count)
        );

        if (tracks.size() < count) {
            throw new ServiceException("航迹名称前缀'" + namePrefix + "'匹配的航迹数量不足，需要" + count + "个，实际只有" + tracks.size() + "个");
        }

        List<String> trackIds = new ArrayList<>();
        for (DataPresetTrackInfo track : tracks) {
            trackIds.add(track.getId());
        }

        return trackIds.subList(0, count);
    }

    /**
     * 获取航迹ID列表（保留原方法作为备用）
     */
    private List<String> getTrackIds(Integer count) {
        List<DataPresetTrackInfo> tracks = dataPresetTrackInfoService.list(
            Wrappers.<DataPresetTrackInfo>lambdaQuery()
                .last("LIMIT " + count)
                    .orderByDesc(DataPresetTrackInfo::getCreateTime)
        );

        if (tracks.size() < count) {
            throw new ServiceException("数据库中的航迹数量不足，需要" + count + "个，实际只有" + tracks.size() + "个");
        }

        List<String> trackIds = new ArrayList<>();
        for (DataPresetTrackInfo track : tracks) {
            trackIds.add(track.getId());
        }

        return trackIds;
    }
    
    /**
     * 获取主表ID列表
     */
    private List<String> getGeneralIds(Integer count) {
        List<String> generalIds = new ArrayList<>();
        
        // 查询所有目标类型的数据表
        List<DataGeneral> generals = dataGeneralService.list(
            Wrappers.<DataGeneral>lambdaQuery()
                .in(DataGeneral::getDataType, 4, 5, 6) // 无人机、无人车、无人艇
        );
        
        if (generals.isEmpty()) {
            throw new ServiceException("未找到目标数据表");
        }
        
        // 按比例分配每种类型的目标数量
        int perTypeCount = count / generals.size();
        int remainder = count % generals.size();
        
        for (int i = 0; i < generals.size(); i++) {
            DataGeneral general = generals.get(i);
            int currentTypeCount = perTypeCount + (i < remainder ? 1 : 0);
            
            for (int j = 0; j < currentTypeCount; j++) {
                generalIds.add(general.getId());
            }
        }
        
        return generalIds;
    }
}
