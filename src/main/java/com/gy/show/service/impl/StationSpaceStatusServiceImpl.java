package com.gy.show.service.impl;

import com.alibaba.fastjson.JSON;
import com.gy.show.runnner.InterBootstrapRunner;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.SysDataMapping;
import com.gy.show.entity.dto.external.SpaceTargetControlDTO;
import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.service.StationSpaceStatusService;
import com.gy.show.socket.BinaryParser;
import com.gy.show.socket.message.FieldDefinition;
import com.gy.show.socket.message.fields.*;
import com.gy.show.socket.server.UdpMulticastSender;
import com.gy.show.util.RedisUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.gy.show.constants.CacheConstant.REAL_TARGET_INFO;

/**
 * 航天测控站状态上报信息
 */
@Slf4j
@Service
public class StationSpaceStatusServiceImpl extends StationServiceImpl implements StationSpaceStatusService {

    @Autowired
    private InterBootstrapRunner runner;

    @Value("#{${target.mapping}}")
    private Map<String, String> targetMapping;

    private AtomicInteger controlMessageCount = new AtomicInteger();

    private AtomicInteger commandMessageCount = new AtomicInteger();

    @Override
    public void packageMessageHead(ByteBuf byteBuf, int type) {
        // 信源地址 4
        byteBuf.writeIntLE(0);

        // 目的地址 4
        byteBuf.writeIntLE(0);

        // 信息类别 4
        byteBuf.writeIntLE(type);

        // 保留 4
        byteBuf.writeIntLE(0);

        // 日期 4
        DateLEField.writeDateField(byteBuf, LocalDate.of(2024, 12, 19));

        // 时间 4
        DateLEField.writeTimeField(byteBuf, LocalTime.now());

        // 长度 4
        byteBuf.writeIntLE(4);
    }

    @Override
    public void packageControlMessage(ByteBuf byteBuf, SpaceTargetControlDTO spaceTargetControlDTO) {
        // BF 过程命令标识 2
        byteBuf.writeShortLE(0x4209);

        // No 命令顺序号 2 每次发送加1
        byteBuf.writeShortLE(controlMessageCount.incrementAndGet());

        // 过程命令
        // 载波输出 1
        byteBuf.writeByte(spaceTargetControlDTO.getMainSwitch());

//        // 输出电平 1
//        byteBuf.writeByte(0);

        // 通道选择 1
//        byteBuf.writeByte(9);

        List<SpaceTargetControlDTO.TargetControlCommand> commands = spaceTargetControlDTO.getCommands()
                .stream().sorted(Comparator.comparing(SpaceTargetControlDTO.TargetControlCommand::getSeq)).collect(Collectors.toList());

        for (SpaceTargetControlDTO.TargetControlCommand command : commands) {
            // 遥控 1
            byteBuf.writeByte(command.getIsOpen());

            // 遥测 1
            byteBuf.writeByte(command.getIsOpen());
        }
    }

    @Override
    public void spaceCommand(SpaceTargetControlDTO spaceTargetControlDTO) {
        log.info("向航天站发送消息：{}", spaceTargetControlDTO);
        ByteBuf byteBuf = Unpooled.buffer();

        // 头
        packageMessageHead(byteBuf, 0xF001);

        // 体
        packageControlMessage(byteBuf, spaceTargetControlDTO);

        // 发送消息给航天
        sendMessage2Space(byteBuf, spaceTargetControlDTO);
    }

    @Override
    public void statusCommand() {
        // TODO 需要确定哪些参数是由前端传入
        ByteBuf byteBuf = Unpooled.buffer();

        // 头
        packageMessageHead(byteBuf, 0xF004);

        // 体
        packageStatusMessage(byteBuf);

        // 发送消息给航天
        SpaceTargetControlDTO spaceTargetControlDTO = new SpaceTargetControlDTO();
        spaceTargetControlDTO.setStationId("_ka");
        sendMessage2Space(byteBuf, spaceTargetControlDTO);
    }

    public Map<String, Object> parseMessageHead(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> headMessage = new LinkedList<>();
        // 信源地址 4
        IntegerField sourceAdd = new IntegerField("sourceAdd");
        headMessage.add(sourceAdd);

        // 目的地址 4
        IntegerField targetAdd = new IntegerField("targetAdd");
        headMessage.add(targetAdd);

        // 信息类别 4
        IntegerField type = new IntegerField("type");
        headMessage.add(type);

        // 保留 4
        IntegerField reverse = new IntegerField("reverse");
        headMessage.add(reverse);

        // 日期 4
        IntegerField date = new IntegerField("date");
        headMessage.add(date);

        // 时间 4
        IntegerField time = new IntegerField("time");
        headMessage.add(time);

        // 长度 4
        IntegerField length = new IntegerField("length");
        headMessage.add(length);

        Map<String, Object> parseResult = BinaryParser.parseFieldsLE(byteBuf, headMessage);
        log.info("解析航天测控站头部信息结果：{}", parseResult);

        return parseResult;
    }

    public Map<String, Object> parseControlMessageBody(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> bodyMessage = new LinkedList<>();

        // ResB 4  响应信息类别：与响应的命令对应
        IntegerField resB = new IntegerField("resB");
        bodyMessage.add(resB);

        // Flag 4
        IntegerField flag = new IntegerField("flag");
        bodyMessage.add(flag);

        // No 2 被响应的命令顺序号
        ShortField seq = new ShortField("seq");
        bodyMessage.add(seq);

        // Result 1
        UnSignedByteField result = new UnSignedByteField("result");
        bodyMessage.add(result);

        Map<String, Object> body = BinaryParser.parseFieldsLE(byteBuf, bodyMessage);
        log.info("航天测控站控制命令响应结果体：{}", body);

        return body;
    }

    @Override
    public Map<String, Object> parseStatusMessageBody(ByteBuf byteBuf) {
        // 命令顺序号 2
        short seq = byteBuf.readShortLE();
        log.info("当前状态信息顺序号：{}", seq);

        Map<String, Object> result = new HashMap<>();
        // 单元1
        parseUnit1(byteBuf, result);

        // 单元2，3
        parseUnit2(byteBuf, result);

        // 单元2，3 skip 196 + 4
//        parseUnit2(byteBuf, result);
        byteBuf.skipBytes(200);

        // 单元4 skip
        parseUnit4(byteBuf, result);

        // 测距单元5、6
        parseUnit56(byteBuf, result);

        // 测距单元5、6 skip 184 + 4
//        parseUnit56(byteBuf, result);
        byteBuf.skipBytes(188);

        // 单元7 skip
        parseUnit7(byteBuf, result);

        // 单元8
        parseUnit8(byteBuf, result);

        // 测距单元9、10 skip
        parseUnit910(byteBuf, result);

        // 测距单元9、10 skip
        parseUnit910(byteBuf, result);

        // 测距单元11 skip
        parseUnit11(byteBuf, result);

        // 单元12 skip
        parseUnit12(byteBuf, result);
        
        // 单元13 skip
        parseUnit13(byteBuf, result);

        return result;
    }

    @Override
    public void parseSpaceMessage(ByteBuf byteBuf, String id) {
        // 查询上报节点将其与数据库中的资源站进行映射
        Map<String, Object> stationInfo = mappingStation(id);

        // 解析头
        Map<String, Object> head = parseMessageHead(byteBuf);

        String type = Integer.toHexString(Integer.parseInt(head.get("type").toString())).toUpperCase();
        switch (type) {
            case "F100":
                // 控制命令响应
                // 解析报文体
                Map<String, Object> body = parseControlMessageBody(byteBuf);

                // TODO 将最终结果发送给前端？前端做延时处理
                break;
            case "F104":
                // 状态信息上报
                Map<String, Object> statusMessage = parseStatusMessageBody(byteBuf);

                // 数据处理
                LinkedList<Map<String, Object>> result = dataHandle(statusMessage, stationInfo);

                // 缓存测控站状态数据
                cacheStationData(result, id);

                // 发送日志
//                sendStationMessage2Front(byteBuf, LogTypeEnum.INFO.getMessage(), "接收到航天测控站状态数据", id);

                // 发送数据结果到前端
                sendMessage2Front(result, WebSocketTypeEnum.FULL_VIEW_SPACE_STATION.getCode());
                break;
            default:
                log.error("未知指令类型：{}", type);
                break;
        }
    }

    private LinkedList<Map<String, Object>> dataHandle(Map<String, Object> statusMessage, Map<String, Object> stationInfo) {
        // 获取每个单元的数据进行整合
        LinkedList<Map<String, Object>> result = new LinkedList<>();
        Map<String, Object> target1 = new HashMap<>();
        result.add(target1);

        Map<String, Object> target2 = new HashMap<>();
        result.add(target2);

        Map<String, Object> target3 = new HashMap<>();
        result.add(target3);

        Map<String, Object> target4 = new HashMap<>();
        result.add(target4);

        for (Object value : statusMessage.values()) {
            List<Map<String, Object>> unit = (List<Map<String, Object>>) value;

            for (int i = 0; i < unit.size(); i++) {
                result.get(i).putAll(unit.get(i));
            }
        }

        // 获取信息
        List<SysDataMapping> mappings = dataMappingService.queryDataMapping(Arrays.asList(4));

        // 对结果做映射，如目标1需要绑定到指定目标ID
        int count = 0;

        for (SysDataMapping mapping : mappings) {
            String[] str = mapping.getDataValue().split("_");
            String generalId = str[0];
            String targetId = str[1];

            Map<String, Object> targetMap;
            String targetInfoInMemory = RedisUtil.StringOps.get(REAL_TARGET_INFO  + targetId);

            // 查询内存，若存在则直接使用
            if (StringUtils.isNotBlank(targetInfoInMemory)) {
                targetMap = JSON.parseObject(targetInfoInMemory, Map.class);
            } else {
                DataGeneral dataGeneral = dataGeneralService.getById(generalId);
                targetMap = commonMapper.getOne(dataGeneral.getTableName(), targetId);

                // 存入内存，避免每次都去查询数据库
                RedisUtil.StringOps.setEx(REAL_TARGET_INFO + targetId, JSON.toJSONString(targetMap), 10, TimeUnit.MINUTES);
            }

            Map<String, Object> targetN = result.get(count);
            targetN.put("targetInfo", targetMap);
            targetN.put("stationInfo", stationInfo);

            // ka 写死频率
            if (stationInfo.get("id").equals("1834055782248001538")) {
                targetN.put("sxgzpl", "21000000000");
                targetN.put("xxgzpl", "30000000000");
            }
            count++;
        }

        return result;
    }

    private void parseUnit13(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 13);
        // 该单元不展示 无需解析，全部跳过即可
        // skip 15 * 4 = 60
        byteBuf.skipBytes(60);
        readEndFlag(byteBuf, 13);
    }

    private void parseUnit12(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 12);
        // 该单元不展示 无需解析，全部跳过即可
        // skip 65 * 4 = 260
        byteBuf.skipBytes(260);
        readEndFlag(byteBuf, 12);
    }

    private void parseUnit11(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 11);
        // 该单元不展示 无需解析，全部跳过即可
        // skip
        byteBuf.skipBytes(58 * 4);
        readEndFlag(byteBuf, 11);
    }

    private void parseUnit910(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 9);
        // 该单元不展示 无需解析，全部跳过即可
        // skip 4 * 4 = 16
        byteBuf.skipBytes(16);

        readEndFlag(byteBuf, 9);
    }

    private void parseUnit56(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 5);
        List<Map<String, Object>> unit56 = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            LinkedList<FieldDefinition> unit56List = new LinkedList<>();

            // 测距位同步锁定指示 1
            UnSignedByteField cjwtbsdzs = new UnSignedByteField("cjwtbsdzs");
            unit56List.add(cjwtbsdzs);

            // 测距帧同步锁定指示 1
            UnSignedByteField cjztbsdzs = new UnSignedByteField("cjztbsdzs");
            unit56List.add(cjztbsdzs);

            // 实测距离值 4
            IntegerField scjlz = new IntegerField("scjlz");
            unit56List.add(scjlz);

            // skip 40
            EmptyField empty1 = new EmptyField("empty2", 40);
            unit56List.add(empty1);

            Map<String, Object> unit56Map = BinaryParser.parseFieldsLE(byteBuf, unit56List);
            log.info("单元5、6解析 --> 目标：{}解析结果：{}", i, unit56Map);

            unit56.add(unit56Map);
        }

        result.put("unit56", unit56);

        readEndFlag(byteBuf, 5);
    }

    private void parseUnit8(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 8);

        List<Map<String, Object>> unit8Map = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            List<FieldDefinition> fields = new ArrayList<>();
            // 遥测位同步锁定指示 1
            UnSignedByteField ycwtbsd = new UnSignedByteField("ycwtbsd");
            fields.add(ycwtbsd);

            // 遥测帧同步锁定指示 1
            UnSignedByteField ycztbsd = new UnSignedByteField("ycztbsd");
            fields.add(ycztbsd);

            // skip 1
            EmptyField empty1 = new EmptyField("empty1", 1);
            fields.add(empty1);

            // Eb/N0 2
            ShortField ebno = new ShortField("ebno");
            fields.add(ebno);

            // skip 64
            EmptyField empty2 = new EmptyField("empty2", 68);
            fields.add(empty2);

            Map<String, Object> unit8 = BinaryParser.parseFieldsLE(byteBuf, fields);
            log.info("单元8 --> 解析结果：{}", unit8);

            unit8Map.add(unit8);
        }

        result.put("unit8", unit8Map);

        readEndFlag(byteBuf, 8);
    }

    private void parseUnit7(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 7);

        // 该单元不展示 无需解析，全部跳过即可
        // skip
        byteBuf.skipBytes(120 * 4);

        readEndFlag(byteBuf, 7);
    }

    private void parseUnit4(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 4);

        // 该单元不展示 无需解析，全部跳过即可
        // skip 7 + 27 * 4 = 115
        byteBuf.skipBytes(115);

        readEndFlag(byteBuf, 4);
    }

    private void parseUnit2(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 2);
        // 目标1、2、3、4遥测、测量参数(依次重复4次，表示不同目标)
        LinkedList<Map<String, Object>> ykycList = new LinkedList<>();
        for (int i = 0; i < 4; i++) {
            LinkedList<FieldDefinition> unit2List = new LinkedList<>();

            // skip 3
            EmptyField empty1 = new EmptyField("empty1", 3);
            unit2List.add(empty1);

            // agc电压 2 界面显示为0
            ShortField agc = new ShortField("agc");
            unit2List.add(agc);

            // 遥测C/N0 2
            ShortField ycc = new ShortField("ycc");
            unit2List.add(ycc);

            // 遥测中频接收参数 实测多普勒频率 8 有符号
            LongField scdplpl = new LongField("scdplpl");
            unit2List.add(scdplpl);

            // skip 8
            EmptyField empty2 = new EmptyField("empty2", 8);
            unit2List.add(empty2);

            // 测量AGC电压 2
            ShortField clagcdy = new ShortField("clagcdy");
            unit2List.add(clagcdy);

            // 测量C/N0 2
            ShortField clcn = new ShortField("clcn");
            unit2List.add(clcn);

            // 测量中频接收参数 实测多普勒频率 8 有符号
            LongField clzpdplpl = new LongField("clzpdplpl");
            unit2List.add(clzpdplpl);

            // 实测速度值 4
            IntegerField scsd = new IntegerField("scsd");
            unit2List.add(scsd);

            // skip 10
            EmptyField empty3 = new EmptyField("empty3", 10);
            unit2List.add(empty3);

            Map<String, Object> targetMap = BinaryParser.parseFieldsLE(byteBuf, unit2List);
            log.info("单元2、3解析 --> 目标：{}解析结果：{}", targetMap);

            ykycList.add(targetMap);
        }

        result.put("unit23", ykycList);

        readEndFlag(byteBuf, 2);
    }

    private void parseUnit1(ByteBuf byteBuf, Map<String, Object> result) {
        readStartFlag(byteBuf, 1);
        // 跳过38个字节
        byteBuf.skipBytes(38);

        // 目标1、2、3、4公共数据(依次重复4次，表示不同目标)
        LinkedList<Map<String, Object>> targetList = new LinkedList<>();
        for (int i = 0; i < 4; i++) {
            LinkedList<FieldDefinition> targetMap = new LinkedList<>();
            // 遥测载波锁定 1
            UnSignedByteField yczbsd = new UnSignedByteField("yczbsd");
            targetMap.add(yczbsd);

            // 遥测伪码锁定 1
            UnSignedByteField ycwmsd = new UnSignedByteField("ycwmsd");
            targetMap.add(ycwmsd);

            // 测量载波锁定 1
            UnSignedByteField clzbsd = new UnSignedByteField("clzbsd");
            targetMap.add(clzbsd);

            // 测量伪码锁定 1
            UnSignedByteField clwmsd = new UnSignedByteField("clwmsd");
            targetMap.add(clwmsd);

            // 卫星遥测锁定指示 1
            UnSignedByteField wxycsd = new UnSignedByteField("wxycsd");
            targetMap.add(wxycsd);

            // 卫星测量锁定指示 1
            UnSignedByteField wxclsd = new UnSignedByteField("wxclsd");
            targetMap.add(wxclsd);

            // 当前跟踪通道 1
            UnSignedByteField dqgztd = new UnSignedByteField("dqgztd");
            targetMap.add(dqgztd);

            // 预报多普勒 4
            IntegerField ybdpl = new IntegerField("ybdpl");
            targetMap.add(ybdpl);

            // 接收遥控总条数 2
            ShortField jsykzts = new ShortField("jsykzts");
            targetMap.add(jsykzts);

            // 遥控发令条数 2
            ShortField ykftts = new ShortField("ykfnts");
            targetMap.add(ykftts);

            // 跳过最后7个字节 无需解析
            EmptyField empty1 = new EmptyField("empty1", 17);
            targetMap.add(empty1);

            // 上行工作频率 8
            LongField sxgzpl = new LongField("sxgzpl");
            targetMap.add(sxgzpl);

            // 上行工作频率 8
            LongField xxgzpl = new LongField("xxgzpl");
            targetMap.add(xxgzpl);

            EmptyField empty2 = new EmptyField("empty2", 6);
            targetMap.add(empty2);

            Map<String, Object> target = BinaryParser.parseFieldsLE(byteBuf, targetMap);
            log.info("目标：{} 解析结果：{}", i, target);
            targetList.add(target);
        }
        result.put("unit1", targetList);

        // 读取结束标识
        readEndFlag(byteBuf, 1);
    }

    private void readStartFlag(ByteBuf byteBuf, int i) {
        short startFlag = byteBuf.readShortLE();
        log.info("单元：{}，开始标识：{}", i, Integer.toHexString(startFlag));
    }

    private void readEndFlag(ByteBuf byteBuf, int unit) {
        short endFlag = byteBuf.readShortLE();
        log.info("单元：{} 结束,结束标识:{}", unit, Integer.toHexString(endFlag));
    }

    private void packageStatusMessage(ByteBuf byteBuf) {
        // 命令顺序号 2
        byteBuf.writeShortLE(commandMessageCount.incrementAndGet());
    }

    private void sendMessage2Space(ByteBuf byteBuf, SpaceTargetControlDTO spaceTargetControlDTO) {
        UdpMulticastSender sender = runner.getSenders().get(ClientTypeEnum.SPACE_STATUS.getMessage() + spaceTargetControlDTO.getStationId());
        sender.sendMessage(byteBuf);
    }
}
