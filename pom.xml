<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gy</groupId>
    <artifactId>wrpt</artifactId>
    <version>1.0</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.3.RELEASE</version>
    </parent>
    <dependencies>
        <!-- web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github</groupId>
            <artifactId>spring-boot-starter-security-jwt</artifactId>
            <version>2.0.2.RELEASE</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-test</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-autoconfigure</artifactId>-->
        <!--        </dependency>-->

        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- kafka -->
        <!--<dependency>-->
        <!--<groupId>org.springframework.kafka</groupId>-->
        <!--<artifactId>spring-kafka</artifactId>-->
        <!--</dependency>-->

        <!--<dependency>-->
        <!--<groupId>org.apache.kafka</groupId>-->
        <!--<artifactId>kafka-clients</artifactId>-->
        <!--<version>2.6.0</version>-->
        <!--</dependency>-->

        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- nacos服务发现 -->
        <!--<dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.2.1.RELEASE</version>
        </dependency>-->

        <!-- nacos配置中心 -->
        <!--<dependency>
             <groupId>com.alibaba.cloud</groupId>
             <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
             <version>2.2.1.RELEASE</version>
         </dependency>-->

        <!-- feign -->
        <!--<dependency>-->
        <!--<groupId>org.springframework.cloud</groupId>-->
        <!--<artifactId>spring-cloud-starter-openfeign</artifactId>-->
        <!--<version>2.2.4.RELEASE</version>-->
        <!--</dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>fastjson</artifactId>-->
        <!--            <version>1.2.73</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>druid</artifactId>-->
        <!--            <version>1.2.5</version>-->
        <!--        </dependency>-->

        <!-- http 客户端 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.12</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>

        <!--mybatis-plus自动代码生成-->
        <!--        <dependency>-->
        <!--            <groupId>com.baomidou</groupId>-->
        <!--            <artifactId>mybatis-plus-generator</artifactId>-->
        <!--            <version>3.2.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.velocity</groupId>-->
        <!--            <artifactId>velocity</artifactId>-->
        <!--            <version>1.7</version>-->
        <!--        </dependency>-->

        <!--生产模型依赖-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.velocity</groupId>-->
        <!--            <artifactId>velocity-engine-core</artifactId>-->
        <!--            <version>2.1</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.cetc10.spaceflight</groupId>
            <artifactId>orbitpre-orbit</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>1.4.0</version>
        </dependency>
        <!-- 20220817 注释掉 -->
        <!--<dependency>-->
        <!--<groupId>redis.clients</groupId>-->
        <!--<artifactId>jedis</artifactId>-->
        <!--<version>3.2.0</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.apache.commons</groupId>-->
        <!--            <artifactId>commons-pool2</artifactId>-->
        <!--            <version>2.6.2</version>-->
        <!--        </dependency>-->

        <!--数据工具包-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.10</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.jdom</groupId>-->
        <!--            <artifactId>jdom2</artifactId>-->
        <!--            <version>2.0.6</version>-->
        <!--        </dependency>-->

        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.7.0</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.7.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.9.5</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.4.6</version>
        </dependency>

        <!--        &lt;!&ndash; 日期转换 &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>joda-time</groupId>-->
        <!--            <artifactId>joda-time</artifactId>-->
        <!--            <version>2.9.9</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.fasterxml</groupId>-->
        <!--            <artifactId>classmate</artifactId>-->
        <!--            <version>1.5.1</version>-->
        <!--        </dependency>-->

                <dependency>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                    <version>2.4</version>
                </dependency>

        <!--<dependency>-->
        <!--<groupId>dom4j</groupId>-->
        <!--<artifactId>dom4j</artifactId>-->
        <!--<version>1.6.1</version>-->
        <!--</dependency>-->

        <!--日志-->
        <!--        <dependency>-->
        <!--            <groupId>org.slf4j</groupId>-->
        <!--            <artifactId>slf4j-api</artifactId>-->
        <!--            <version>1.7.30</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.slf4j</groupId>-->
        <!--            <artifactId>slf4j-log4j12</artifactId>-->
        <!--            <version>1.7.30</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>log4j</groupId>-->
        <!--            <artifactId>log4j</artifactId>-->
        <!--            <version>1.2.17</version>-->
        <!--        </dependency>-->

        <!--        &lt;!&ndash; 上传ftp &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>commons-net</groupId>-->
        <!--            <artifactId>commons-net</artifactId>-->
        <!--            <version>3.6</version>-->
        <!--        </dependency>-->

        <!-- websocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework</groupId>-->
        <!--            <artifactId>spring-websocket</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.eclipse.jetty.websocket</groupId>-->
        <!--            <artifactId>websocket-api</artifactId>-->
        <!--        </dependency>-->

        <!-- json解析 -->
        <!--        <dependency>-->
        <!--            <groupId>com.jayway.jsonpath</groupId>-->
        <!--            <artifactId>json-path</artifactId>-->
        <!--            <version>2.4.0</version>-->
        <!--        </dependency>-->


        <!--计算轨道数据-->
        <!--<dependency>-->
        <!--<groupId>orbitpre</groupId>-->
        <!--<artifactId>orbitpre</artifactId>-->
        <!--<version>1.0</version>-->
        <!--</dependency>-->

        <!--<dependency>
            <groupId>satellite</groupId>
            <artifactId>satelliteV1</artifactId>
            <version>1.0</version>
        </dependency>-->
        <!--解析xml-->
        <!--        <dependency>-->
        <!--            <groupId>org.dom4j</groupId>-->
        <!--            <artifactId>dom4j</artifactId>-->
        <!--            <version>2.1.1</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>


        <dependency>
            <groupId>org.orekit</groupId>
            <artifactId>orekit</artifactId>
            <version>10.3</version>
        </dependency>

        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
            <version>1.16.1</version>
        </dependency>

        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-geojson</artifactId>
            <version>19.0</version>
        </dependency>

        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-shapefile</artifactId>
            <version>19.0</version>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-swing</artifactId>
            <version>19.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.eclipse.emf</groupId>-->
<!--            <artifactId>org.eclipse.emf.common</artifactId>-->
<!--            <version>2.15.0</version>-->
<!--        </dependency>-->

        <!-- Netty -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.66.Final</version>
        </dependency>
    </dependencies>

    <repositories>
        <!-- 添加 OSGeo 仓库 -->
        <repository>
            <id>osgeo</id>
            <name>OSGeo Release Repository</name>
            <url>https://repo.osgeo.org/repository/release/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <!--<resources>-->
    <!--<resource>-->
    <!--<directory>src/main/java</directory>-->
    <!--<includes>-->
    <!--<includes>**/*.xml</includes>-->
    <!--</includes>-->
    <!--</resource>-->
    <!--<resource>-->
    <!--<directory>src/mian/resources</directory>-->
    <!--<includes>-->
    <!--<includes>**/*.xml</includes>-->
    <!--<includes>**/*.properties</includes>-->
    <!--<includes>**.*.yml</includes>-->
    <!--<includes>**.*.yaml</includes>-->
    <!--</includes>-->
    <!--</resource>-->
    <!--</resources>-->
</project>